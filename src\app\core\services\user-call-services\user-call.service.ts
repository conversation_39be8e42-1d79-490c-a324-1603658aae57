import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { SocketService } from '../socket-services/socket.service';
import { RingtoneService } from '../ringtone-services/ringtone.service';
import { CallsService } from '../calls-services/calls.service';
import { IUser } from '../../interfaces/auth-interfaces/iuser-model';
import { IStartCallSessionRequest } from '../../interfaces/calls/istart-call-session-request';
import { IStartCallSessionResponse } from '../../interfaces/calls/istart-call-session-response';
import { IJoinCallSessionRequest } from '../../interfaces/calls/ijoin-call-session-request';
import { IEndCallSessionRequest } from '../../interfaces/calls/iend-call-session-request';
import { CallTypesEnum } from '../../enums/call-types-enum.enum';

export interface IUserCallData {
  callId: string;
  callerId: string;
  callerName: string;
  callerAvatar?: string;
  receiverId: string;
  receiverName?: string;
  roomName?: string;
  timestamp: Date;
  jitsiRoomName?: string;
  jitsiToken?: string;
  jitsiUrl?: string;
}

export interface ICallState {
  isInCall: boolean;
  isReceivingCall: boolean;
  isCalling: boolean;
  currentCall?: IUserCallData;
  callStatus: 'idle' | 'calling' | 'ringing' | 'connected' | 'ended';
}

@Injectable({
  providedIn: 'root'
})
export class UserCallService {
  private callStateSubject = new BehaviorSubject<ICallState>({
    isInCall: false,
    isReceivingCall: false,
    isCalling: false,
    callStatus: 'idle'
  });

  public callState$ = this.callStateSubject.asObservable();
  private currentUser: IUser | null = null;

  constructor(
    private socketService: SocketService,
    private ringtoneService: RingtoneService,
    private callsService: CallsService
  ) {
    this.initializeSocketListeners();
    this.loadCurrentUser();
    this.registerUserWithServer();
  }

  private loadCurrentUser() {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      this.currentUser = JSON.parse(userStr);
    }
  }

  private registerUserWithServer() {
    // Wait a bit for socket connection to establish
    setTimeout(() => {
      if (this.currentUser && this.currentUser.id) {
        this.socketService.emit('register-user', {
          userId: this.currentUser.id,
          userName: this.currentUser.fullNameEn || this.currentUser.fullNameAr || 'Unknown',
          userAvatar: this.currentUser.proPic
        });
        console.log('User registered with call server:', this.currentUser.id);
      }
    }, 1000);
  }

  private initializeSocketListeners() {
    // Listen for incoming calls
    this.socketService.on('incoming-call', (callData: IUserCallData) => {
      this.handleIncomingCall(callData);
    });

    // Listen for call accepted
    this.socketService.on('call-accepted', (callData: IUserCallData) => {
      this.handleCallAccepted(callData);
    });

    // Listen for call rejected
    this.socketService.on('call-rejected', (callData: IUserCallData) => {
      this.handleCallRejected(callData);
    });

    // Listen for call ended
    this.socketService.on('call-ended', (callData: IUserCallData) => {
      this.handleCallEnded(callData);
    });

    // Listen for user busy
    this.socketService.on('user-busy', (callData: IUserCallData) => {
      this.handleUserBusy(callData);
    });

    // Listen for user offline
    this.socketService.on('user-offline', (callData: IUserCallData) => {
      this.handleUserOffline(callData);
    });
  }

  // Initiate a call to another user
  initiateCall(receiverId: string, receiverName: string): void {
    if (!this.currentUser) {
      console.error('No current user found');
      return;
    }

    // First create call session via backend API
    // Try with notification interface field names in case backend expects those
    const startCallRequest: any = {
      // Original interface fields
      modrId: this.currentUser.id!,
      toUserId: receiverId,
      callModuleEntityId: this.currentUser.id!,
      callModuleTypehuffazId: CallTypesEnum.FreeRecitation,
      isVideoMute: false,

      // Also try notification interface field names
      modId: this.currentUser.id!,
      usrId: receiverId,

      // Optional fields
      dayId: undefined,
      day: undefined,
      tskId: undefined,
      batId: undefined
    };

    console.log('Starting call session with request:', startCallRequest);
    console.log('Current user:', this.currentUser);
    console.log('User token:', this.currentUser?.token);
    console.log('API URL:', this.callsService.startCallSessionURL);

    this.callsService.startCallSession(startCallRequest).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          const callSessionData = response.data as IStartCallSessionResponse;

          const callData: IUserCallData = {
            callId: callSessionData.callId,
            callerId: this.currentUser!.id!,
            callerName: this.currentUser!.fullNameEn || this.currentUser!.fullNameAr || 'Unknown',
            callerAvatar: this.currentUser!.proPic,
            receiverId: receiverId,
            receiverName: receiverName,
            roomName: callSessionData.roomName,
            timestamp: new Date(),
            jitsiRoomName: callSessionData.roomName,
            jitsiToken: callSessionData.token,
            jitsiUrl: callSessionData.url
          };

          // Update local state
          this.updateCallState({
            isInCall: false,
            isReceivingCall: false,
            isCalling: true,
            currentCall: callData,
            callStatus: 'calling'
          });

          // Send call notification via Socket.io
          this.socketService.emit('call-user', callData);
        } else {
          console.error('Failed to start call session:', response.message);
        }
      },
      error: (error) => {
        console.error('Error starting call session:', error);
        console.error('Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          error: error.error
        });
      }
    });
  }

  // Accept an incoming call
  acceptCall(callData: IUserCallData): void {
    this.ringtoneService.stop();

    // First, notify via Socket.io for immediate response
    this.socketService.emit('accept-call', callData);

    // Then create formal call session via backend API
    const joinCallRequest: IJoinCallSessionRequest = {
      callId: callData.callId,
      usrId: this.currentUser?.id!,
      callModuleTypehuffazId: CallTypesEnum.FreeRecitation, // Using FreeRecitation for user-to-user calls
      isMod: false
    };

    this.callsService.joinCallSession(joinCallRequest).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          // Backend call session created successfully
          const callSessionData = response.data as IStartCallSessionResponse;

          this.updateCallState({
            isInCall: true,
            isReceivingCall: false,
            isCalling: false,
            currentCall: {
              ...callData,
              jitsiRoomName: callSessionData.roomName,
              jitsiToken: callSessionData.token,
              jitsiUrl: callSessionData.url
            },
            callStatus: 'connected'
          });

          // Open Jitsi call
          this.openJitsiCall(callSessionData);
        } else {
          console.error('Failed to join call session:', response.message);
          this.rejectCall(callData);
        }
      },
      error: (error) => {
        console.error('Error joining call session:', error);
        this.rejectCall(callData);
      }
    });
  }

  // Reject an incoming call
  rejectCall(callData: IUserCallData): void {
    this.ringtoneService.stop();
    
    this.updateCallState({
      isInCall: false,
      isReceivingCall: false,
      isCalling: false,
      currentCall: undefined,
      callStatus: 'idle'
    });

    // Notify caller that call was rejected
    this.socketService.emit('reject-call', callData);
  }

  // End current call
  endCall(): void {
    const currentState = this.callStateSubject.value;
    if (currentState.currentCall) {
      // End call session via backend API
      const endCallRequest: IEndCallSessionRequest = {
        callId: currentState.currentCall.callId,
        usrId: this.currentUser?.id!,
        isMod: currentState.currentCall.callerId === this.currentUser?.id,
        callModuleTypehuffazId: CallTypesEnum.FreeRecitation
      };

      this.callsService.endCallSession(endCallRequest).subscribe({
        next: (response) => {
          if (response.isSuccess) {
            console.log('Call session ended successfully');
          } else {
            console.error('Failed to end call session:', response.message);
          }
        },
        error: (error) => {
          console.error('Error ending call session:', error);
        }
      });

      // Notify other party via Socket.io
      this.socketService.emit('end-call', currentState.currentCall);
    }

    this.ringtoneService.stop();
    this.updateCallState({
      isInCall: false,
      isReceivingCall: false,
      isCalling: false,
      currentCall: undefined,
      callStatus: 'idle'
    });
  }

  private handleIncomingCall(callData: IUserCallData): void {
    // Check if already in a call
    const currentState = this.callStateSubject.value;
    if (currentState.isInCall || currentState.isCalling) {
      // Send busy signal
      this.socketService.emit('user-busy', callData);
      return;
    }

    this.updateCallState({
      isInCall: false,
      isReceivingCall: true,
      isCalling: false,
      currentCall: callData,
      callStatus: 'ringing'
    });

    // Play ringtone
    this.ringtoneService.play();
  }

  private handleCallAccepted(callData: IUserCallData): void {
    this.updateCallState({
      isInCall: true,
      isReceivingCall: false,
      isCalling: false,
      currentCall: callData,
      callStatus: 'connected'
    });
  }

  private handleCallRejected(callData: IUserCallData): void {
    this.updateCallState({
      isInCall: false,
      isReceivingCall: false,
      isCalling: false,
      currentCall: undefined,
      callStatus: 'idle'
    });
  }

  private handleCallEnded(callData: IUserCallData): void {
    this.ringtoneService.stop();
    this.updateCallState({
      isInCall: false,
      isReceivingCall: false,
      isCalling: false,
      currentCall: undefined,
      callStatus: 'idle'
    });
  }

  private handleUserBusy(callData: IUserCallData): void {
    this.updateCallState({
      isInCall: false,
      isReceivingCall: false,
      isCalling: false,
      currentCall: undefined,
      callStatus: 'idle'
    });
    console.log('User is busy:', callData.receiverId);
  }

  private handleUserOffline(callData: IUserCallData): void {
    this.updateCallState({
      isInCall: false,
      isReceivingCall: false,
      isCalling: false,
      currentCall: undefined,
      callStatus: 'idle'
    });
    console.log('User is offline:', callData.receiverId);
  }

  private updateCallState(newState: ICallState): void {
    this.callStateSubject.next(newState);
  }

  private generateCallId(): string {
    return 'call_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  // Get current call state
  getCurrentCallState(): ICallState {
    return this.callStateSubject.value;
  }

  // Debug method - call this from browser console to test API
  testStartCallAPI(receiverId: string = 'test-user-id'): void {
    if (!this.currentUser) {
      console.error('No current user found. Please login first.');
      return;
    }

    const testRequest: any = {
      // Try both field name formats
      modrId: this.currentUser.id!,
      toUserId: receiverId,
      callModuleEntityId: this.currentUser.id!,
      callModuleTypehuffazId: CallTypesEnum.FreeRecitation,
      isVideoMute: false,

      // Also notification interface format
      modId: this.currentUser.id!,
      usrId: receiverId
    };

    console.log('=== API TEST DEBUG ===');
    console.log('URL:', this.callsService.startCallSessionURL);
    console.log('Request payload:', JSON.stringify(testRequest, null, 2));
    console.log('Current user:', this.currentUser);
    console.log('Auth token:', this.currentUser.token ? 'Present' : 'Missing');

    this.callsService.startCallSession(testRequest).subscribe({
      next: (response) => {
        console.log('✅ API Success:', response);
      },
      error: (error) => {
        console.error('❌ API Error:', error);
        console.error('Error status:', error.status);
        console.error('Error message:', error.message);
        console.error('Error body:', error.error);
      }
    });
  }

  private openJitsiCall(callSessionData: IStartCallSessionResponse): void {
    // Open Jitsi call in a new window or redirect
    const jitsiUrl = callSessionData.url;
    if (jitsiUrl) {
      window.open(jitsiUrl, '_blank');
    } else {
      console.error('No Jitsi URL provided in call session response');
    }
  }
}
