import { Component, OnInit } from '@angular/core';
declare var JitsiMeetExternalAPI: any;
import { CallService } from 'src/app/core/services/calls-services/calls.service';
import { SocketService } from 'src/app/core/services/socket-services/socket.service';
@Component({
  selector: 'app-incoming-call',
  templateUrl: './incoming-call.component.html',
  styleUrls: ['./incoming-call.component.scss']
})
export class IncomingCallComponent implements OnInit {

   domain = 'meet.jit.si';
  roomName = '';
  jitsiApi: any;
  userId = 'student-id'; // Change as needed
  teacherId = 'teacher-id'; // Change as needed

  constructor(
    private callService: CallService,
    private socketService: SocketService
  ) {}

  ngOnInit() {
    // Listen for teacher joining the call
    this.socketService.listen('teacher-joined').subscribe((data) => {
      console.log('Teacher joined:', data);
    });

    // Optional: Listen if call ended
    this.socketService.listen('call-ended').subscribe(() => {
      this.jitsiApi?.dispose();
    });
  }

  startCall() {
    const payload = {
      modrId: this.userId,
      toUserId: this.teacherId,
      callModuleEntityId: 'uuid-entity',
      callModuleTypehuffazId: 0,
      dayId: 'uuid-day',
      tskId: 'uuid-task',
      isVideoMute: false,
      batId: 'uuid-bat',
    };

    this.callService.startCallSession(payload).subscribe((res: any) => {
      this.roomName = res.callId ?? 'room-' + new Date().getTime();

      this.socketService.emit('call-started', {
        toUserId: this.teacherId,
        callId: this.roomName,
      });

      this.initJitsi(this.roomName);
    });
  }

  joinCall(callId: string) {
    const joinPayload = {
      callId: callId,
      usrId: this.userId,
      callModuleTypehuffazId: 0,
      isMod: false,
    };

    this.callService.joinCallSession(joinPayload).subscribe(() => {
      this.initJitsi(callId);
    });
  }

  endCall() {
    const payload = {
      callId: this.roomName,
      usrId: this.userId,
      isMod: true,
      callModuleTypehuffazId: 0,
    };

    this.callService.endCallSession(payload).subscribe(() => {
      this.jitsiApi?.dispose();
      this.socketService.emit('call-ended', { callId: this.roomName });
    });
  }

  initJitsi(room: string) {
    const options = {
      roomName: room,
      width: '100%',
      height: 500,
      parentNode: document.getElementById('jitsi-container'),
      configOverwrite: {},
      interfaceConfigOverwrite: {},
    };
    this.jitsiApi = new JitsiMeetExternalAPI(this.domain, options);
  }

  ngOnDestroy(): void {
    this.jitsiApi?.dispose();
    this.socketService.disconnect();
  }

}
