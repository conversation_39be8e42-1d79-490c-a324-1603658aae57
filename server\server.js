const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');

const app = express();
const server = http.createServer(app);

// Configure CORS for Socket.io
const io = socketIo(server, {
  cors: {
    origin: "http://localhost:4200", // Angular dev server
    methods: ["GET", "POST"],
    credentials: true
  }
});

app.use(cors());
app.use(express.json());

// Store connected users
const connectedUsers = new Map();

io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Handle user registration
  socket.on('register-user', (userData) => {
    console.log('User registered:', userData);
    connectedUsers.set(userData.userId, {
      socketId: socket.id,
      userData: userData
    });
    socket.userId = userData.userId;
  });

  // Handle call initiation
  socket.on('call-user', (callData) => {
    console.log('Call initiated:', callData);
    const receiverConnection = connectedUsers.get(callData.receiverId);
    
    if (receiverConnection) {
      // Send incoming call notification to receiver
      io.to(receiverConnection.socketId).emit('incoming-call', callData);
      console.log('Incoming call sent to:', callData.receiverId);
    } else {
      // User not online
      socket.emit('user-offline', callData);
      console.log('User offline:', callData.receiverId);
    }
  });

  // Handle call acceptance
  socket.on('accept-call', (callData) => {
    console.log('Call accepted:', callData);
    const callerConnection = connectedUsers.get(callData.callerId);
    
    if (callerConnection) {
      io.to(callerConnection.socketId).emit('call-accepted', callData);
    }
  });

  // Handle call rejection
  socket.on('reject-call', (callData) => {
    console.log('Call rejected:', callData);
    const callerConnection = connectedUsers.get(callData.callerId);
    
    if (callerConnection) {
      io.to(callerConnection.socketId).emit('call-rejected', callData);
    }
  });

  // Handle call end
  socket.on('end-call', (callData) => {
    console.log('Call ended:', callData);
    // Notify both parties
    const callerConnection = connectedUsers.get(callData.callerId);
    const receiverConnection = connectedUsers.get(callData.receiverId);
    
    if (callerConnection) {
      io.to(callerConnection.socketId).emit('call-ended', callData);
    }
    if (receiverConnection) {
      io.to(receiverConnection.socketId).emit('call-ended', callData);
    }
  });

  // Handle user busy
  socket.on('user-busy', (callData) => {
    console.log('User busy:', callData);
    const callerConnection = connectedUsers.get(callData.callerId);
    
    if (callerConnection) {
      io.to(callerConnection.socketId).emit('user-busy', callData);
    }
  });

  // Handle disconnect
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
    if (socket.userId) {
      connectedUsers.delete(socket.userId);
      console.log('Removed user from connected users:', socket.userId);
    }
  });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Socket.io server running on port ${PORT}`);
  console.log('Connected users will be tracked for call notifications');
});
