import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { initializeApp } from '@firebase/app';
import { getDatabase } from '@firebase/database';
import { TranslateService } from '@ngx-translate/core';
import { environment } from 'src/environments/environment';
import { BaseConstantModel } from './core/ng-model/base-constant-model';
import { LanguageService } from './core/services/language-services/language.service';
import { SocketService } from './core/services/socket-services/socket.service';
import { RingtoneService } from './core/services/ringtone-services/ringtone.service';
import { UserCallService } from './core/services/user-call-services/user-call.service';

// const config = {
//   apiKey: "AIzaSyBz6L0qamX2o9J8APMJ2QlKF5jDhECicI4",
//   authDomain: "hoffaz.firebaseapp.com",
//   databaseURL: "https://hoffaz-default-rtdb.firebaseio.com",
//   projectId: "hoffaz",
//   storageBucket: "hoffaz.appspot.com",
//   messagingSenderId: "870269866675",
//   appId: "1:870269866675:web:8eaf3bca7841a83b52d6c6",
//   measurementId: "G-CG7X412XTV"
// };



@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
  
})

export class AppComponent implements OnInit{
  title = 'huffaz-app';

  // Call functionality properties
  userIdToCall: string = '';
  showCallInterface: boolean = false;

  constructor(public translate:TranslateService,
    private router: Router,
    private socketService: SocketService,
    private RingtoneService: RingtoneService,
    private langService: LanguageService,
    private userCallService: UserCallService){
      // firebase.initializeApp(config);
    // // Get a reference to the database service
    // var database = firebase.database();
    }

  ngOnInit(): void {
    this.langService.initLang();

    // firebase.initializeApp(environment.firebaseConfig);

    // Get a reference to the database service
    // var database = firebase.database();
    const app = initializeApp(environment.firebaseConfig);
    const database = getDatabase(app);

    // Add debug access to window object for testing
    if (!environment.production) {
      (window as any).debugCallService = this.userCallService;
      console.log('🔧 Debug: Call service available at window.debugCallService');
      console.log('🔧 Debug: Test API with: window.debugCallService.testStartCallAPI("user-id")');
    }

    // this.socketService.on('connect', () => {
    //   let socket = this.socketService.getSocket();
    //   console.log('Socket connected:', socket.id);
    // });

  //   this.socketService.on('ringing', () => {
  //     setTimeout(() => {
  //       this.RingtoneService.play();
  //     }, 5000);
  //   });
}

  showHeader(){
    return !BaseConstantModel.NO_HEADER_ROUTES.includes(this.router.url.split('?')[0]) && this.router.url !== '/';
  }

  // Call functionality methods
  toggleCallInterface(): void {
    this.showCallInterface = !this.showCallInterface;
  }

  initiateCall(): void {
    if (this.userIdToCall.trim()) {
      this.userCallService.initiateCall(this.userIdToCall.trim(), 'User ' + this.userIdToCall);
      this.userIdToCall = '';
      this.showCallInterface = false;
    }
  }

  endCurrentCall(): void {
    this.userCallService.endCall();
  }
}
