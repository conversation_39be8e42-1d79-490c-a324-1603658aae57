<div class="container-fluid" *ngIf="showHeader()">
  <div class="row" >
    <div class="col-1 p-0">
      <app-sidebar></app-sidebar>
    </div>
    <div class="col-11">
      <div class="row">
      <div class="col-12 p-0">
  <app-header></app-header>
      </div>
      </div>


        <!-- <app-notification></app-notification> -->
        <router-outlet></router-outlet>
        <app-my-loader></app-my-loader>

    </div>
  </div>


</div>
  <!-- Call Interface Button -->
  <div class="call-interface-container">
    <button class="btn-call-toggle" (click)="toggleCallInterface()"
            [class.active]="showCallInterface">
      <i class="fas fa-phone"></i>
    </button>

    <!-- Call Interface Panel -->
    <div class="call-interface-panel" *ngIf="showCallInterface">
      <div class="call-panel-header">
        <h5>{{ 'MAKE_CALL' }}</h5>
        <button class="btn-close" (click)="toggleCallInterface()">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="call-panel-body">
        <div class="form-group">
          <label for="userIdInput">{{ 'USER Id' }}</label>
          <input type="text"
                 id="userIdInput"
                 class="form-control"
                 [(ngModel)]="userIdToCall"
                 [placeholder]="'ENTER_USER_ID' | translate">
        </div>

        <div class="call-actions">
          <button class="btn btn-success"
                  (click)="initiateCall()"
                  [disabled]="!userIdToCall.trim()">
            <i class="fas fa-phone"></i>
            {{ 'CALL' }}
          </button>

          <button class="btn btn-danger"
                  (click)="endCurrentCall()">
            <i class="fas fa-phone-slash"></i>
            {{ 'END_CALL' }}
          </button>
        </div>
      </div>
    </div>
  </div>

<div class="container-fluid px-0 land_page" *ngIf="!showHeader()">
    <router-outlet name='baseRouter'></router-outlet>
</div>

<!-- Incoming Call Component - Always present -->
<app-incoming-call></app-incoming-call>

<!-- <app-footer></app-footer> -->